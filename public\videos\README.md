# Video Files

## S10 Feature Video by <PERSON> - Galaxy Unpacked 2019

### 🎯 Target Video
**Official Samsung Galaxy Unpacked 2019 Video**
- **YouTube URL**: https://www.youtube.com/watch?v=USDvd7fhbd0
- **Event**: Galaxy Unpacked February 2019 (S10 Launch)
- **Presenter**: <PERSON> (Samsung Director of Product Marketing)
- **Content**: S10 features presentation and demonstration

### 📁 Setup Instructions

1. **Download the video**:
   - Go to: https://www.youtube.com/watch?v=USDvd7fhbd0
   - Use a YouTube downloader (like yt-dlp, 4K Video Downloader, or online tools)
   - Download in MP4 format, preferably 1080p

2. **Prepare the file**:
   - **Rename to**: `s10-feature-suzanne-desilva.mp4`
   - **Place in**: `public/videos/s10-feature-suzanne-desilva.mp4`

3. **Optional - Extract specific segment**:
   - If you want only <PERSON>'s presentation segment (not the full event)
   - Use video editing software to extract her portion
   - Typically appears around the middle of the presentation

### 🔧 Technical Requirements
- **Format**: MP4 (H.264 codec recommended)
- **Resolution**: 1080p or higher
- **File size**: Optimize for web (under 50MB if possible)
- **Audio**: Include audio track (will be muted for background play)

### 🚀 How it works
Once the video file is placed correctly:
1. The website will automatically detect and use the S10 video
2. It plays as background video on the hero section
3. Includes scroll-based scaling and opacity effects
4. Falls back to placeholder video if file not found

### 📋 Current Status
- ✅ Folder structure created
- ✅ Code updated to reference S10 video
- ⏳ **Waiting for video file**: `s10-feature-suzanne-desilva.mp4`
- ✅ Fallback video configured

### 🎬 Alternative: YouTube Embed (Not Recommended for Background)
If you prefer to use YouTube directly (though autoplay is limited):
- The video can be embedded in the gallery section instead
- Background autoplay from YouTube has browser restrictions
